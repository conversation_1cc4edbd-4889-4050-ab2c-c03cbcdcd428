import React from "react";
import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { logout } from "@/app/server/actions";
import { getServerUser } from "@/utils/get_server_user";

export default async function DashboardPage() {
  const supabase = await createClient();

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    redirect("/server/login");
  }

  const userData = await getServerUser();

  const { data: users } = await supabase.from("users").select("*");

  return (
    <div className="flex flex-col gap-4 justify-center items-center min-h-screen w-full max-w-3xl mx-auto">
      <div className="flex justify-between gap-4 w-full">
        <h1 className="text-3xl font-semibold">Dashboard</h1>
        <form action={logout}>
          <button
            type="submit"
            className="font-semibold px-4 py-2 rounded-md bg-foreground text-background"
          >
            Logout
          </button>
        </form>
      </div>
    </div>
  );
}
