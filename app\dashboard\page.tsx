import React from "react";
import { createClient } from "@/lib/supabase/client";

export default async function DashboardPage() {
  const supabase = await createClient();

    const { data: user, error } = await supabase.auth.getUser();
    if (!user || error) {
      redirect("/login");
    }

    const { data: users } = await supabase.from("users").select("*");

    console.log(users);

    return <div>{JSON.stringify(user)}</div>;
  }
}
