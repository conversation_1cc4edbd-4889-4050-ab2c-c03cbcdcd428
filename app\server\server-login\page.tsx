import React from "react";
import { login } from "@/app/server/actions";

import Input from "@/components/ui/Input";
import SubmitButton from "@/components/SubmitButton";
import ServerMessage from "@/components/ui/ServerMessage";

export default function LoginPage() {
  return (
    <div className="flex flex-col gap-4 justify-center items-center min-h-screen">
      <h1 className="text-3xl font-semibold">Login</h1>
      <ServerMessage />
      <form action={login} className="grid gap-4 w-full max-w-sm">
        <Input
          label="Email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
        />
        <Input label="Password" name="password" type="password" />
        <SubmitButton>Login</SubmitButton>
      </form>
    </div>
  );
}
