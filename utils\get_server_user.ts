import { createClient } from "@/lib/supabase/server";
import prisma from "@/lib/prisma/prisma";

export async function getServerUser() {
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return null;
  }

  // Get user data from the database
  const { data: userData } = await supabase
    .from("profiles")
    .select("first_name, last_name, role")
    .eq("user_id", user.id)
    .single();

  if (!userData) {
    return null;
  }

  return {
    ...userData,
    email: user.email,
  };
}
