generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")   
  directUrl = env("DIRECT_URL")     
}

model Profile {
  id         Int    @id @default(autoincrement())
  user_id    String @unique @db.Uuid      
  first_name String
  last_name  String
  role       Role   @default(employee)    
  email      String @unique

  @@map("profiles")                       
}

enum Role {
  employee
  admin
  manager
  trainer
}
