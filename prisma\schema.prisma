generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public"]
}

model Profile {
  id         BigInt @id @default(autoincrement())
  user_id    String @unique @db.Uuid
  first_name String
  last_name  String
  role       Role   @default(employee)

  @@map("profiles")
  @@schema("public")
}

enum Role {
  employee
  admin
  manager
  trainer

  @@schema("public")
}
