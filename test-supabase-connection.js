const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://jxjnhndjjzcudmyrvsgd.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp4am5obmRqanpjdWRteXJ2c2dkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NzYyNTIyNCwiZXhwIjoyMDczMjAxMjI0fQ.yYhmrlg7oBubVqUEn2_sl6rfDAme26i9TBHCJsAbfdg';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSupabaseConnection() {
  try {
    console.log('Testing Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
      console.error('Full error:', error);
    } else {
      console.log('✅ Supabase connected successfully!');
      console.log('Data:', data);
    }
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
  }
}

testSupabaseConnection();
