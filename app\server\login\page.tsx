import React from "react";
import { login } from "@/app/server/actions";
import Link from "next/link";

import Input from "@/components/ui/Input";
import SubmitButton from "@/components/ui/SubmitButton";
import ServerMessage from "@/components/ui/ServerMessage";

export default function LoginPage() {
  return (
    <div className="flex flex-col gap-4 justify-center items-center min-h-screen">
      <h1 className="text-3xl font-semibold">Login</h1>
      <ServerMessage />
      <form action={login} className="grid gap-4 w-full max-w-sm">
        <Input
          label="Email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
        />
        <Input label="Password" name="password" type="password" />
        <SubmitButton>Login</SubmitButton>
      </form>
      <p className="text-center">
        Don't have an account?{" "}
        <Link className="font-semibold underline" href="/server/signup">
          Signup
        </Link>
      </p>
    </div>
  );
}
